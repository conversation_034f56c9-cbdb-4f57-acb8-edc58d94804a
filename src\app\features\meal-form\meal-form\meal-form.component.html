<div class="container">
  <header class="page-header">
    <h1 id="page-title">{{ sharedDataService.title }}</h1>
    <p id="page-description">{{ sharedDataService.description }}</p>
  </header>

  <main class="page-content">
    <form
      class="meal-form"
      [formGroup]="mealForm"
      (ngSubmit)="onSubmit()"
      role="form"
      aria-labelledby="page-title"
      aria-describedby="page-description"
      novalidate
    >
      <p class="required-fields-note" aria-label="Required fields information">
        <span aria-hidden="true">*</span> indicates required fields
      </p>

      <div class="form-row">
        <mat-form-field appearance="fill">
          <mat-label>Time Available</mat-label>
          <mat-select formControlName="timeAvailable" required>
            <mat-option value="5">5 minutes</mat-option>
            <mat-option value="15">15 minutes</mat-option>
            <mat-option value="30">30 minutes</mat-option>
            <mat-option value="45">45 minutes</mat-option>
            <mat-option value="60">1 hour</mat-option>
          </mat-select>
          <mat-error *ngIf="isFieldInvalid('timeAvailable')"
            >Please select time available</mat-error
          >
        </mat-form-field>

        <mat-form-field appearance="fill">
          <mat-label>Number of People</mat-label>
          <input
            matInput
            type="number"
            formControlName="numberOfPeople"
            min="1"
            max="20"
            required
          />
          <mat-error *ngIf="isFieldInvalid('numberOfPeople')"
            >Please enter number of people</mat-error
          >
        </mat-form-field>
      </div>

      <mat-form-field appearance="fill">
        <mat-label>Ingredients</mat-label>
        <textarea matInput formControlName="ingredients" rows="4" required></textarea>
        <mat-error *ngIf="isFieldInvalid('ingredients')">Please list ingredients</mat-error>
      </mat-form-field>

      <fieldset class="dietary-restrictions-fieldset">
        <legend>Dietary Restrictions (Optional)</legend>
        <div formGroupName="dietaryRestrictions">
          <div class="checkbox-group">
            <mat-checkbox formControlName="glutenFree">Gluten Free</mat-checkbox>
          </div>
          <div class="checkbox-group">
            <mat-checkbox formControlName="dairyFree">Dairy Free</mat-checkbox>
          </div>
          <div class="checkbox-group">
            <mat-checkbox formControlName="vegetarian">Vegetarian</mat-checkbox>
          </div>
          <div class="checkbox-group">
            <mat-checkbox formControlName="peanutAllergy">Peanut Allergy</mat-checkbox>
          </div>
          <div class="checkbox-group">
            <mat-checkbox formControlName="other">Other Restriction</mat-checkbox>
          </div>

          <mat-form-field
            *ngIf="mealForm.get('dietaryRestrictions.other')?.value"
            appearance="fill"
          >
            <mat-label>Specify Restriction</mat-label>
            <input matInput formControlName="otherRestriction" />
            <mat-error *ngIf="isOtherRestrictionInvalid()">Please specify restriction</mat-error>
          </mat-form-field>
        </div>
      </fieldset>

      <fieldset class="picky-eaters-fieldset">
        <legend>Additional Preferences</legend>
        <mat-checkbox formControlName="pickyEaters">Picky Eaters</mat-checkbox>
      </fieldset>

      <div class="form-actions">
        <button mat-raised-button color="primary" type="submit" [disabled]="!mealForm.valid">
          <span *ngIf="!isSubmitting">Get Recipe Suggestions</span>
          <span *ngIf="isSubmitting">
            <mat-spinner diameter="20"></mat-spinner>
            Generating...
          </span>
        </button>
      </div>

      <div *ngIf="!mealForm.valid && mealForm.touched" class="submit-error" role="alert">
        Please fill in all required fields
      </div>
    </form>
  </main>
</div>
