import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpErrorResponse } from '@angular/common/http';
import { map, catchError } from 'rxjs/operators';
import { Observable, throwError } from 'rxjs';
import { LlmResponse } from '../models/llm-response.model';
import { MealSuggestion } from '../models/meal-suggestion.interface';
import { environment } from '../../../environments/environment';
import { ChatMessage } from '../models/chat-message.interface';
import { GroqRequestOptions } from '../models/groq-request-options.interface';

/**
 * Service for interacting with Groq LLM API
 */
@Injectable({ providedIn: 'root' })
export class LLMGROQService {
  private readonly apiUrl = environment.groqApiUrl;
  private readonly model = environment.groqModel;
  private readonly apiKey = environment.groqApiKey;

  // Default system prompt
  private readonly systemPrompt =
    'You are a helpful dinner assistant for busy parents. Always respond with valid JSON.';

  constructor(private http: HttpClient) {}

  /**
   * Get meal suggestions based on user prompt
   * @param prompt User's meal request prompt
   * @param options Optional configuration for the request
   * @returns Observable of MealSuggestion
   */
  getMealSuggestions(
    prompt: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
    },
  ): Observable<MealSuggestion> {
    const headers = this.getAuthHeaders();
    const body = this.createRequestBody(prompt, options);

    return this.http.post<LlmResponse>(this.apiUrl, body, { headers }).pipe(
      map((res) => this.parseResponse(res)),
      catchError((error) => this.handleError(error)),
    );
  }

  /**
   * Create authentication headers for Groq API
   */
  private getAuthHeaders(): HttpHeaders {
    return new HttpHeaders({
      'Content-Type': 'application/json',
      Authorization: `Bearer ${this.apiKey}`,
    });
  }

  /**
   * Create request body for Groq API
   */
  private createRequestBody(
    prompt: string,
    options?: {
      temperature?: number;
      maxTokens?: number;
      systemPrompt?: string;
    },
  ): GroqRequestOptions {
    return {
      model: this.model,
      messages: [
        {
          role: 'system',
          content: options?.systemPrompt || this.systemPrompt,
        },
        { role: 'user', content: prompt },
      ],
      temperature: options?.temperature || 0.7,
      max_tokens: options?.maxTokens || 1000,
    };
  }

  /**
   * Parse LLM response into MealSuggestion
   */
  private parseResponse(res: LlmResponse): MealSuggestion {
    const content = res.choices?.[0]?.message?.content ?? '{}';
    try {
      // Extract JSON from the response (in case the LLM adds extra text)
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const jsonString = jsonMatch ? jsonMatch[0] : content;
      return JSON.parse(jsonString) as MealSuggestion;
    } catch (e) {
      console.error('Failed to parse JSON response:', e);
      // Return a fallback object with the raw content
      return {
        name: 'Parsing Error',
        description: 'Could not parse the recipe data.',
        ingredients: [],
        preparationSteps: [],
        cookingTime: '',
        rawResponse: content,
      } as MealSuggestion;
    }
  }

  /**
   * Handle HTTP errors from Groq API
   */
  private handleError(error: HttpErrorResponse): Observable<never> {
    let errorMessage = 'Failed to get meal suggestions. Please try again.';

    if (error.status === 401 || error.status === 403) {
      errorMessage = 'Authentication error. Please check your API key.';
    } else if (error.status === 429) {
      errorMessage = 'Rate limit exceeded. Please try again later.';
    } else if (error.status >= 500) {
      errorMessage = 'Groq service is currently unavailable. Please try again later.';
    }

    console.error('API Error:', error);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * Get takeout recommendations based on location and preferences
   */
  getTakeoutRecommendations(
    location: { lat: number; lng: number },
    preferences: {
      numberOfPeople: number;
      dietaryRestrictions: any;
      cuisine?: string;
    }
  ): Observable<TakeoutSuggestion[]> {
    const prompt = `Find takeout restaurants near coordinates ${location.lat}, ${location.lng} that:
- Serve ${preferences.numberOfPeople} people
- Accommodate dietary restrictions: ${this.formatDietaryRestrictions(preferences.dietaryRestrictions)}
- Offer quick pickup/delivery (under 30 minutes)

Return JSON array of 5 restaurants:
[{
  "name": "Restaurant Name",
  "cuisine": "Cuisine Type", 
  "estimatedTime": "15-20 minutes",
  "address": "Full Address",
  "phone": "Phone Number",
  "dietaryOptions": ["gluten-free", "vegetarian"]
}]`;

    return this.getMealSuggestions(prompt).pipe(
      map(response => JSON.parse(response.rawResponse || '[]'))
    );
  }

  /**
   * Format dietary restrictions for prompt
   */
  private formatDietaryRestrictions(restrictions: any): string {
    if (Array.isArray(restrictions)) {
      return restrictions.join(', ');
    }
    return Object.keys(restrictions)
      .filter(key => restrictions[key])
      .join(', ');
  }
}
