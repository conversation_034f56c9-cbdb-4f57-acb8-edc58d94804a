// Shared container and layout styles
.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 1rem;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
  
  h1 {
    margin: 0 0 0.5rem 0;
    color: #1976d2;
  }
  
  p {
    margin: 0;
    color: #666;
  }
}

// Error and loading states
.error-message {
  background-color: #ffebee;
  border: 1px solid #ffcdd2;
  border-radius: 4px;
  padding: 1rem;
  color: #d32f2f;
  text-align: center;
  margin: 1rem 0;
}

.loading-section {
  text-align: center;
  padding: 2rem;
}

// Form styles
.form-row {
  display: flex;
  gap: 1rem;
}

.form-row mat-form-field {
  flex: 1;
}

.form-actions {
  text-align: center;
  margin-top: 1rem;
}

.submit-error {
  color: #d32f2f;
  text-align: center;
  margin-top: 0.5rem;
}

// Fieldset styles
.dietary-restrictions-fieldset,
.picky-eaters-fieldset {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  margin: 1rem 0;
}

.checkbox-group {
  margin-bottom: 0.5rem;
}

// Responsive design
@media (max-width: 768px) {
  .container {
    padding: 0.5rem;
  }
  
  .form-row {
    flex-direction: column;
  }
}
