import { ComponentFixture, TestBed } from '@angular/core/testing';
import { Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { MealTakeoutComponent } from './meal-takeout.component';
import { LLMGROQService } from '../../../core/services/llm-groq.service';
import { SharedDataService } from '../../../core/services/shared-data.service';
import { GeolocationService } from '../../../core/services/geolocation.service';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatListModule } from '@angular/material/list';
import { NoopAnimationsModule } from '@angular/platform-browser/animations';

describe('MealTakeoutComponent', () => {
  let component: MealTakeoutComponent;
  let fixture: ComponentFixture<MealTakeoutComponent>;
  let llmGroqServiceSpy: jasmine.SpyObj<LLMGROQService>;
  let sharedDataService: SharedDataService;
  let geolocationServiceSpy: jasmine.SpyObj<GeolocationService>;
  let router: Router;

  // Mock takeout suggestion data
  const mockTakeoutSuggestion: TakeoutSuggestion = {
    name: 'Test Restaurant',
    description: 'A delicious test restaurant',
    address: '123 Test St',
    phone: '************',
    website: 'http://test.com',
    cuisine: 'Test cuisine',
    priceRange: '$$',
    hours: '11am-10pm',
  };

  beforeEach(async () => {
    // Create spy objects for LLMGROQService and GeolocationService
    const llmGroqServiceSpy = jasmine.createSpyObj('LLMGROQService', ['getTakeoutRecommendations']);
    const geolocationServiceSpy = jasmine.createSpyObj('GeolocationService', [
      'getCurrentPosition',
    ]);

    await TestBed.configureTestingModule({
      imports: [
        MealTakeoutComponent,
        MatCardModule,
        MatButtonModule,
        MatProgressSpinnerModule,
        MatListModule,
        NoopAnimationsModule,
      ],
      providers: [
        SharedDataService,
        { provide: LLMGROQService, useValue: llmGroqServiceSpy },
        { provide: GeolocationService, useValue: geolocationServiceSpy },
        { provide: Router, useValue: { navigate: jasmine.createSpy('navigate') } },
      ],
    }).compileComponents();

    fixture = TestBed.createComponent(MealTakeoutComponent);
    component = fixture.componentInstance;
    llmGroqServiceSpy = TestBed.inject(LLMGROQService) as jasmine.SpyObj<LLMGROQService>;
    sharedDataService = TestBed.inject(SharedDataService);
    geolocationServiceSpy = TestBed.inject(
      GeolocationService,
    ) as jasmine.SpyObj<GeolocationService>;
    router = TestBed.inject(Router);

    // Setup default spy behavior
    llmGroqServiceSpy.getTakeoutRecommendations.and.returnValue(of([mockTakeoutSuggestion]));
  });

  it('should create', () => {
    fixture.detectChanges();
    expect(component).toBeTruthy();
  });
});
