@import './../../../shared/shared-styles.scss';

.meal-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 600px;
  margin: 0 auto;
}

.form-row {
  display: flex;
  gap: 1rem;
}

.form-row mat-form-field {
  flex: 1;
}

.dietary-restrictions-fieldset,
.picky-eaters-fieldset {
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 1rem;
  margin: 0;
}

.checkbox-group {
  margin-bottom: 0.5rem;
}

.form-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

.submit-error {
  color: #d32f2f;
  text-align: center;
  margin-top: 0.5rem;
}

@media (max-width: 768px) {
  .form-row {
    flex-direction: column;
  }
}
